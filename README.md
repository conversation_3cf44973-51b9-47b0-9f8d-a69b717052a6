# WebXR AR Image Tracking Demo

This project demonstrates WebXR AR functionality combined with OpenCV.js for image tracking.

## Fixed Issues

The following issues have been resolved:

1. **WebXR Session Initialization**: Added proper support checking and error handling
2. **Camera Access in AR Mode**: Fixed camera stream management between CV and XR modes
3. **Matrix Transformations**: Corrected coordinate system conversions between OpenCV and Three.js
4. **Error Handling**: Added comprehensive error handling and status reporting
5. **Visual Feedback**: Improved 3D objects and debug information
6. **Session Management**: Added proper session end handling

## Requirements

- **HTTPS**: WebXR requires HTTPS. Use a local server with SSL or deploy to a secure host
- **Compatible Device**: Android device with ARCore support or iOS device with ARKit
- **Compatible Browser**: 
  - Chrome for Android (recommended)
  - Samsung Internet
  - Firefox Reality
  - Safari on iOS (limited support)

## Setup Instructions

1. **Serve over HTTPS**:
   ```bash
   # Using Python (if you have it installed)
   python -m http.server 8000 --bind 0.0.0.0
   
   # Or using Node.js http-server
   npx http-server -p 8000 -a 0.0.0.0
   ```

2. **Access from mobile device**:
   - Find your computer's IP address
   - Open `https://YOUR_IP:8000` on your mobile browser
   - Accept the security warning (for self-signed certificates)

## Usage Instructions

1. **Generate Test Images**:
   - Open `test-image.html` in your browser
   - Download and print one of the test patterns
   - Use A4 size or larger for better tracking

2. **Test Camera and Tracking**:
   - Open `index.html` on your mobile device
   - Click "Start CV (camera)" to test camera access
   - Upload the test image you downloaded
   - Point camera at the printed pattern
   - You should see green corners drawn around the detected pattern

3. **Start AR Mode**:
   - Once tracking works reliably, click "Start WebXR AR"
   - Grant camera and motion permissions
   - Point at the printed pattern
   - You should see a 3D object (green box with red sphere) appear on the pattern

## Troubleshooting

### WebXR Not Working

**Problem**: "WebXR not supported" or "WebXR AR not supported"
- **Solution**: Ensure you're using a compatible browser on a supported device
- **Check**: Visit `https://immersiveweb.dev/` to test WebXR support

**Problem**: AR session fails to start
- **Solution**: 
  - Ensure HTTPS is being used
  - Check browser permissions for camera and motion sensors
  - Try restarting the browser
  - Clear browser cache and data

### Camera Issues

**Problem**: Camera not working in AR mode
- **Solution**: 
  - Grant camera permissions when prompted
  - Check if camera is being used by another app
  - Try refreshing the page
  - Restart the browser

### Tracking Issues

**Problem**: Pattern not detected
- **Solution**:
  - Ensure good lighting conditions
  - Print pattern larger (A4 minimum)
  - Use high contrast printing
  - Keep pattern flat and unobstructed
  - Move camera closer/further to find optimal distance

**Problem**: 3D object not appearing in AR
- **Solution**:
  - First test with "Start CV" to ensure tracking works
  - Check debug info for "Matches" and "Inliers" counts
  - Ensure XR status shows "Active"
  - Try moving device slowly around the pattern

### Performance Issues

**Problem**: App running slowly
- **Solution**:
  - Close other browser tabs
  - Restart the browser
  - Use a more powerful device
  - Reduce pattern complexity

## Browser-Specific Notes

### Chrome for Android
- Best support for WebXR AR
- Requires ARCore-compatible device
- Enable "WebXR Device API" in chrome://flags if needed

### Samsung Internet
- Good WebXR support
- May require enabling experimental features

### Safari on iOS
- Limited WebXR support
- May work on newer iOS versions with WebXR enabled

## Development Notes

- The app uses OpenCV.js for image tracking and Three.js for 3D rendering
- Coordinate system conversion between OpenCV and Three.js is handled automatically
- The tracking runs in parallel with the XR render loop
- Anchors are created when targets are first detected

## Common Error Messages

- **"WebXR not supported"**: Use a compatible browser/device
- **"Camera error"**: Check permissions and camera availability
- **"Transform error"**: Usually indicates coordinate system issues (should be fixed)
- **"Target lost"**: Normal when pattern goes out of view
- **"XR session ended"**: Normal when exiting AR mode

## Tips for Best Results

1. Use high-contrast printed patterns
2. Ensure good lighting
3. Keep patterns flat and stable
4. Start with CV mode to test tracking before AR
5. Move device slowly and steadily
6. Keep pattern in view during AR session startup

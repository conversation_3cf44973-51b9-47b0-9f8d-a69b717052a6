<!doctype html>
<html>

<head>
    <meta charset="utf-8" />
    <title>WebXR + OpenCV Image Tracking (demo)</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            font-family: sans-serif;
        }

        #ui {
            position: absolute;
            top: 8px;
            left: 8px;
            z-index: 20;
            background: rgba(0, 0, 0, 0.4);
            padding: 8px;
            border-radius: 6px;
            color: #fff
        }

        video {
            display: none;
        }

        /* used only for OpenCV */
        canvas#debug {
            position: absolute;
            right: 8px;
            bottom: 8px;
            width: 240px;
            height: 160px;
            border: 1px solid #333;
            z-index: 20;
        }
    </style>
</head>

<body>
    <div id="ui">
        <input id="fileTarget" type="file" accept="image/*"><br /><br />
        <button id="startCv">Start CV (camera)</button>
        <button id="startXR">Start WebXR AR</button><br />
        <label>Physical target width (m): <input id="targetWidth" value="0.2" style="width:70px" /></label>
        <div id="status">status: idle</div>
        <div id="debug-info" style="font-size: 12px; margin-top: 8px; color: #ccc;"></div>
    </div>

    <video id="video" autoplay playsinline></video>
    <canvas id="debug"></canvas>

    <!-- Three.js -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/build/three.min.js"></script>
    <!-- OpenCV.js (use official or local build) -->
    <script async src="https://docs.opencv.org/4.x/opencv.js"></script>

    <script>
        /*
          Demo: image-tracking (OpenCV.js) + WebXR anchors (Three.js)
          - This is a practical starter: not production-ready.
          - Some WebXR APIs are experimental; handle gracefully if not present.
        */

        let video = document.getElementById('video');
        let debugCanvas = document.getElementById('debug');
        let dbgCtx = debugCanvas.getContext('2d');

        let targets = []; // {id, imgElement, keypoints, descriptors, width_px, height_px, physicalWidth_m}
        let orb = null;
        let bf = null;

        let cvReady = false;
        let trackingState = { found: false, lastPose: null, targetId: null };

        const statusEl = document.getElementById('status');
        const debugInfoEl = document.getElementById('debug-info');
        function setStatus(s) { statusEl.innerText = 'status: ' + s; }
        function setDebugInfo(s) { debugInfoEl.innerText = s; }

        // Wait for OpenCV to be ready
        cv = window.cv;
        if (cv && cv['onRuntimeInitialized']) {
            cv['onRuntimeInitialized'] = () => {
                cvReady = true;
                orb = new cv.ORB(500);
                bf = new cv.BFMatcher();
                setStatus('OpenCV ready');
            };
        } else {
            // fallback in case cv already ready
            const interval = setInterval(() => {
                if (window.cv && window.cv.ORB) {
                    clearInterval(interval);
                    cvReady = true;
                    orb = new cv.ORB(500);
                    bf = new cv.BFMatcher();
                    setStatus('OpenCV ready');
                }
            }, 200);
        }

        // getUserMedia for CV
        document.getElementById('startCv').addEventListener('click', async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: "environment" }, audio: false });
                video.srcObject = stream;
                await video.play();
                debugCanvas.width = 640; debugCanvas.height = 480;
                setStatus('Camera started');
                runCvLoop(); // start detection loop
            } catch (err) {
                console.error(err);
                setStatus('Camera error: ' + err.message);
            }
        });

        // Load target image at runtime
        document.getElementById('fileTarget').addEventListener('change', (e) => {
            const f = e.target.files[0];
            if (!f) return;
            const img = new Image();
            img.onload = () => {
                // compute ORB features
                if (!cvReady) { alert('OpenCV not ready yet'); return; }
                let mat = cv.imread(img);
                cv.cvtColor(mat, mat, cv.COLOR_RGBA2GRAY);
                let kp = new cv.KeyPointVector();
                let desc = new cv.Mat();
                orb.detectAndCompute(mat, new cv.Mat(), kp, desc);
                const id = 't_' + Date.now();
                targets.push({
                    id, imgElement: img, keypoints: kp, descriptors: desc,
                    width_px: mat.cols, height_px: mat.rows,
                    physicalWidth_m: parseFloat(document.getElementById('targetWidth').value) || 0.2
                });
                mat.delete();
                setStatus('Loaded target ' + id + ' (kps: ' + kp.size() + ')');
            };
            img.src = URL.createObjectURL(f);
        });

        // ---------- detection & pose estimation loop (runs parallel to XR) ----------
        let processing = false;
        async function runCvLoop() {
            if (processing) return;
            processing = true;
            const cap = new cv.VideoCapture(video);

            // mats reused
            let frame = new cv.Mat(video.videoHeight || 480, video.videoWidth || 640, cv.CV_8UC4);
            let gray = new cv.Mat();
            let kpFrame = new cv.KeyPointVector();
            let descFrame = new cv.Mat();

            // matching params
            function ratioTest(matchesArr) {
                // classic Lowe ratio on matches from BFMatcher.knnMatch
                let good = [];
                for (let m of matchesArr) {
                    if (m.length >= 2) {
                        if (m[0].distance < 0.75 * m[1].distance) good.push(m[0]);
                    }
                }
                return good;
            }

            while (video.readyState >= 2) {
                try {
                    cap.read(frame);
                    cv.cvtColor(frame, gray, cv.COLOR_RGBA2GRAY);
                    orb.detectAndCompute(gray, new cv.Mat(), kpFrame, descFrame);

                    // for each target try to match
                    for (let t of targets) {
                        if (t.descriptors.empty() || descFrame.empty()) continue;
                        // knnMatch to apply ratio test
                        // NOTE: OpenCV.js BFMatcher.knnMatch returns array of arrays
                        let knn = new cv.DMatchVectorVector();
                        bf.knnMatch(descFrame, t.descriptors, knn, 2);
                        // convert to JS arrays for ratio test
                        let arr = [];
                        for (let i = 0; i < knn.size(); i++) {
                            let vec = knn.get(i);
                            let sub = [];
                            for (let j = 0; j < vec.size(); j++) {
                                sub.push({ distance: vec.get(j).distance, queryIdx: vec.get(j).queryIdx, trainIdx: vec.get(j).trainIdx });
                            }
                            arr.push(sub);
                            vec.delete();
                        }
                        knn.delete();

                        const good = ratioTest(arr);
                        if (good.length > 10) {
                            // build point arrays for homography: matched points in frame and in target image
                            let srcPts = [];
                            let dstPts = [];
                            for (let m of good) {
                                // queryIdx -> kpFrame, trainIdx -> t.keypoints
                                const qkp = kpFrame.get(m.queryIdx);
                                const tkp = t.keypoints.get(m.trainIdx);
                                srcPts.push(qkp.pt.x, qkp.pt.y); // frame points
                                dstPts.push(tkp.pt.x, tkp.pt.y); // target image points
                            }
                            let srcMat = cv.matFromArray(good.length, 1, cv.CV_32FC2, srcPts);
                            let dstMat = cv.matFromArray(good.length, 1, cv.CV_32FC2, dstPts);
                            let mask = new cv.Mat();
                            // find homography from target -> frame (we used dst->src ordering)
                            let H = cv.findHomography(dstMat, srcMat, cv.RANSAC, 5, mask);

                            if (!H.empty()) {
                                // we have homography. Estimate pose via solvePnP:
                                // we need 3D object points of the target plane: use (0,0,0), (w,0,0), (w,h,0), (0,h,0) in meters
                                // then map their 2D target image coords through homography -> image points in frame
                                const w_px = t.width_px, h_px = t.height_px;
                                const w_m = t.physicalWidth_m;
                                const scale = w_m / w_px;
                                // object points in meters
                                let objPts = [
                                    0, 0, 0,
                                    w_m, 0, 0,
                                    w_m, h_px * scale, 0,
                                    0, h_px * scale, 0
                                ];
                                // target image 2D corners
                                let corners = [0, 0, w_px, 0, w_px, h_px, 0, h_px];
                                // project corners by homography to get their positions in camera image
                                let cornersMat = cv.matFromArray(4, 1, cv.CV_32FC2, corners);
                                let projCorners = new cv.Mat();
                                cv.perspectiveTransform(cornersMat, projCorners, H);
                                // imagePoints for solvePnP
                                let imgPtsArr = [];
                                for (let i = 0; i < 4; i++) {
                                    imgPtsArr.push(projCorners.floatAt(i, 0 * 2 + 0), projCorners.floatAt(i, 0 * 2 + 1));
                                }

                                // estimate camera intrinsics (approx) from video size and a guessed focal length.
                                // For better result: get real intrinsics or try to derive from XRView.projectionMatrix (advanced).
                                const fx = video.videoWidth; // naive guess: focal ~ width (px)
                                const fy = fx;
                                const cx = video.videoWidth / 2;
                                const cy = video.videoHeight / 2;
                                let cameraMat = cv.matFromArray(3, 3, cv.CV_64FC1, [fx, 0, cx, 0, fy, cy, 0, 0, 1]);
                                let dist = cv.Mat.zeros(4, 1, cv.CV_64F);

                                let objMat = cv.matFromArray(4, 1, cv.CV_32FC3, objPts); // 4x1 3D
                                let imgMat = cv.matFromArray(4, 1, cv.CV_32FC2, imgPtsArr);

                                let rvec = new cv.Mat(), tvec = new cv.Mat(), inliers = new cv.Mat();
                                let ok = cv.solvePnPRansac(objMat, imgMat, cameraMat, dist, rvec, tvec, false, 100, 8.0, 0.99, inliers, cv.SOLVEPNP_ITERATIVE);

                                if (ok && inliers.rows > 3) {
                                    // we have camera pose of target (rvec, tvec): target->camera transform
                                    // convert to Three.js matrix and create anchor in WebXR if available
                                    trackingState.found = true;
                                    trackingState.lastPose = { rvec: rvec, tvec: tvec, camMat: cameraMat };
                                    trackingState.targetId = t.id;
                                    setStatus('Target found: ' + t.id + ' inliers=' + inliers.rows);
                                    setDebugInfo(`Matches: ${good.length}, Inliers: ${inliers.rows}, XR: ${xrSession ? 'Active' : 'Inactive'}`);

                                    // draw debug overlay of corners
                                    dbgCtx.clearRect(0, 0, debugCanvas.width, debugCanvas.height);
                                    dbgCtx.drawImage(video, 0, 0, debugCanvas.width, debugCanvas.height);
                                    dbgCtx.strokeStyle = 'lime'; dbgCtx.lineWidth = 3;
                                    dbgCtx.beginPath();
                                    for (let i = 0; i < 4; i++) {
                                        const x = projCorners.data32F[i * 2] * debugCanvas.width / video.videoWidth;
                                        const y = projCorners.data32F[i * 2 + 1] * debugCanvas.height / video.videoHeight;
                                        if (i === 0) dbgCtx.moveTo(x, y); else dbgCtx.lineTo(x, y);
                                    }
                                    dbgCtx.closePath(); dbgCtx.stroke();

                                    // Cleanups
                                    projCorners.delete(); cornersMat.delete();
                                    imgMat.delete(); objMat.delete(); inliers.delete();

                                    // We will create anchor/update Three.js object in XR render loop (see below)
                                } else {
                                    // no good pnp
                                    setStatus('Homography found but PnP failed');
                                    rvec.delete(); tvec.delete(); inliers.delete();
                                }

                                // free mats
                                H.delete(); mask.delete(); srcMat.delete(); dstMat.delete();
                            } else {
                                H.delete();
                                setStatus('Homography empty');
                            }
                        } else {
                            // No good matches found, reset tracking state
                            if (trackingState.found) {
                                trackingState.found = false;
                                trackingState.lastPose = null;
                                trackingState.targetId = null;
                                if (threeAnchorObject) {
                                    threeAnchorObject.visible = false;
                                }
                                setStatus('Target lost');
                                setDebugInfo('No matches found');
                            }
                        } // end if good matches
                    } // end for each target
                } catch (err) {
                    console.error('cv loop err', err);
                    setStatus('CV error: ' + err.message);
                }

                // sleep ~300ms to reduce CPU (adjustable)
                await new Promise(r => setTimeout(r, 300));
            } // end while

            // delete mats when stopping (not implemented)
            processing = false;
        }

        // ------------------- WebXR + Three.js setup -------------------

        let renderer, scene, camera3;
        let xrSession = null;
        let xrRefSpace = null;
        let xrAnchor = null;
        let threeAnchorObject = null;

        // init Three renderer overlay (works outside XR too)
        function initThree() {
            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.xr.enabled = true;
            document.body.appendChild(renderer.domElement);

            scene = new THREE.Scene();
            camera3 = new THREE.PerspectiveCamera(70, window.innerWidth / window.innerHeight, 0.01, 20);
            scene.add(camera3);

            // Create a more visible 3D object for anchor visualization
            const group = new THREE.Group();

            // Main box
            const geo = new THREE.BoxGeometry(0.1, 0.1, 0.02);
            const mat = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
            const box = new THREE.Mesh(geo, mat);
            group.add(box);

            // Add coordinate axes for better orientation
            const axesHelper = new THREE.AxesHelper(0.05);
            group.add(axesHelper);

            // Add a small sphere on top
            const sphereGeo = new THREE.SphereGeometry(0.02, 8, 6);
            const sphereMat = new THREE.MeshLambertMaterial({ color: 0xff0000 });
            const sphere = new THREE.Mesh(sphereGeo, sphereMat);
            sphere.position.set(0, 0, 0.03);
            group.add(sphere);

            threeAnchorObject = group;
            threeAnchorObject.visible = false;
            scene.add(threeAnchorObject);

            // Add some lighting for better visibility
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1);
            scene.add(directionalLight);
        }

        // Start camera for XR tracking
        async function startXRCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: "environment",
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    },
                    audio: false
                });
                video.srcObject = stream;
                await video.play();
                debugCanvas.width = 640;
                debugCanvas.height = 480;
                setStatus('XR Camera started - tracking enabled');
                runCvLoop(); // start detection loop
            } catch (err) {
                console.error('XR Camera error:', err);
                setStatus('XR Camera error: ' + err.message);
            }
        }

        // Start WebXR session
        document.getElementById('startXR').addEventListener('click', async () => {
            // stop camera stream before XR
            if (video.srcObject) {
                video.srcObject.getTracks().forEach(t => t.stop());
                video.srcObject = null;
            }

            // Check WebXR support
            if (!navigator.xr) {
                alert('WebXR not supported on this device/browser');
                setStatus('WebXR not supported');
                return;
            }

            // Check if immersive-ar is supported
            const isSupported = await navigator.xr.isSessionSupported('immersive-ar');
            if (!isSupported) {
                alert('WebXR AR not supported on this device');
                setStatus('WebXR AR not supported');
                return;
            }

            try {
                setStatus('Requesting XR session...');
                initThree();

                // Request session with optional features to avoid failures
                const sessionInit = {
                    requiredFeatures: ['local'],
                    optionalFeatures: ['anchors', 'hit-test', 'dom-overlay'],
                    domOverlay: { root: document.body }
                };

                xrSession = await navigator.xr.requestSession('immersive-ar', sessionInit);
                renderer.xr.setSession(xrSession);
                xrRefSpace = await xrSession.requestReferenceSpace('local');
                setStatus('XR session started - move device to see AR');

                // Start camera access for OpenCV tracking in XR mode
                await startXRCamera();

                // XR render loop
                renderer.setAnimationLoop((timestamp, xrFrame) => {
                    if (!xrFrame) return;

                    // get viewer pose
                    const pose = xrFrame.getViewerPose(xrRefSpace);
                    if (!pose) return;

                    // if we detect target via OpenCV and have rvec/tvec -> create/update anchor
                    if (trackingState.found && trackingState.lastPose) {
                        try {
                            // convert rvec/tvec (target->camera) into a matrix (camera->target) or target->world:
                            // Steps:
                            //  - rvec,tvec are transform from object (target) -> camera coordinate.
                            //  - we want transform from target -> world reference
                            //  - viewerPose gives transform from viewer(camera) -> world
                            //  => target->world = viewerTransform * cameraFromTargetTransform

                            // build target->camera matrix (4x4)
                            const rvec = trackingState.lastPose.rvec;
                            const tvec = trackingState.lastPose.tvec;

                            // convert rvec to rotation matrix (Rodrigues)
                            let R = new cv.Mat();
                            cv.Rodrigues(rvec, R);

                            // Create transformation matrix (target to camera)
                            // Note: OpenCV uses different coordinate system than Three.js
                            let targetToCam = new THREE.Matrix4();
                            targetToCam.set(
                                R.doubleAt(0, 0), R.doubleAt(0, 1), R.doubleAt(0, 2), tvec.data64F[0],
                                R.doubleAt(1, 0), R.doubleAt(1, 1), R.doubleAt(1, 2), tvec.data64F[1],
                                R.doubleAt(2, 0), R.doubleAt(2, 1), R.doubleAt(2, 2), tvec.data64F[2],
                                0, 0, 0, 1
                            );

                            // Get camera to world transform from XR pose
                            const view = pose.views[0];
                            const camPos = view.transform.position;
                            const camOrient = view.transform.orientation;

                            const camQuat = new THREE.Quaternion(camOrient.x, camOrient.y, camOrient.z, camOrient.w);
                            const camPosV = new THREE.Vector3(camPos.x, camPos.y, camPos.z);
                            const cameraToWorld = new THREE.Matrix4();
                            cameraToWorld.makeRotationFromQuaternion(camQuat);
                            cameraToWorld.setPosition(camPosV);

                            // Calculate target to world transform
                            const targetToWorld = new THREE.Matrix4();
                            targetToWorld.multiplyMatrices(cameraToWorld, targetToCam);

                            // Position the anchor object
                            threeAnchorObject.matrix.copy(targetToWorld);
                            threeAnchorObject.matrixAutoUpdate = false;
                            threeAnchorObject.visible = true;

                            R.delete();
                        } catch (transformError) {
                            console.error('Transform error:', transformError);
                            setStatus('Transform error: ' + transformError.message);
                        }

                        // Create an XRAnchor when first detection occurs (if supported)
                        if (!xrAnchor && xrFrame.createAnchor) {
                            try {
                                // createAnchor expects an XRRigidTransform or position/orientation in reference space
                                // Convert targetToWorld into position+quaternion
                                const pos = new THREE.Vector3();
                                const quat = new THREE.Quaternion();
                                const scale = new THREE.Vector3();
                                targetToWorld.decompose(pos, quat, scale);
                                const xrTransform = new XRRigidTransform(
                                    { x: pos.x, y: pos.y, z: pos.z },
                                    { x: quat.x, y: quat.y, z: quat.z, w: quat.w }
                                );
                                xrFrame.createAnchor(xrTransform, xrRefSpace).then(anchor => {
                                    xrAnchor = anchor;
                                    setStatus('XR Anchor created');
                                    // optionally attach a Three.js object to anchors update in onanchorupdate events (not implemented)
                                }).catch(e => {
                                    console.warn('createAnchor error', e);
                                });
                            } catch (e) {
                                console.warn('XR anchor create not supported', e);
                            }
                        }
                    }

                    // render Three.js scene
                    renderer.render(scene, camera3);
                });

                // Handle session end
                xrSession.addEventListener('end', () => {
                    setStatus('XR session ended');
                    xrSession = null;
                    xrRefSpace = null;
                    xrAnchor = null;
                    if (threeAnchorObject) {
                        threeAnchorObject.visible = false;
                    }
                    // Stop camera stream
                    if (video.srcObject) {
                        video.srcObject.getTracks().forEach(t => t.stop());
                        video.srcObject = null;
                    }
                });

            } catch (err) {
                console.error(err);
                setStatus('XR error: ' + err.message);
            }
        });
    </script>
</body>

</html>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Image Generator for AR Tracking</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-image {
            border: 2px solid #333;
            margin: 20px 0;
            display: inline-block;
            background: white;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AR Tracking Test Images</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Right-click on any test image below and save it to your device</li>
                <li>Print the image on paper (recommended size: A4 or larger)</li>
                <li>Open the main AR app in your browser</li>
                <li>Click "Start CV (camera)" first to test camera access</li>
                <li>Upload the saved image using the file input</li>
                <li>Point your camera at the printed image</li>
                <li>Once tracking works, click "Start WebXR AR" to enter AR mode</li>
            </ol>
        </div>

        <h2>Test Image 1: QR-like Pattern</h2>
        <canvas id="pattern1" class="test-image" width="400" height="400"></canvas>
        <br>
        <button onclick="downloadCanvas('pattern1', 'ar-test-pattern-1.png')">Download Pattern 1</button>

        <h2>Test Image 2: Feature-rich Pattern</h2>
        <canvas id="pattern2" class="test-image" width="400" height="400"></canvas>
        <br>
        <button onclick="downloadCanvas('pattern2', 'ar-test-pattern-2.png')">Download Pattern 2</button>

        <h2>Test Image 3: Text Pattern</h2>
        <canvas id="pattern3" class="test-image" width="400" height="400"></canvas>
        <br>
        <button onclick="downloadCanvas('pattern3', 'ar-test-pattern-3.png')">Download Pattern 3</button>
    </div>

    <script>
        // Generate test patterns
        function generatePattern1() {
            const canvas = document.getElementById('pattern1');
            const ctx = canvas.getContext('2d');
            
            // White background
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 400, 400);
            
            // Black border
            ctx.fillStyle = 'black';
            ctx.fillRect(0, 0, 400, 20);
            ctx.fillRect(0, 0, 20, 400);
            ctx.fillRect(380, 0, 20, 400);
            ctx.fillRect(0, 380, 400, 20);
            
            // Create a grid pattern with various shapes
            ctx.fillStyle = 'black';
            for (let i = 0; i < 8; i++) {
                for (let j = 0; j < 8; j++) {
                    const x = 50 + i * 40;
                    const y = 50 + j * 40;
                    if ((i + j) % 3 === 0) {
                        ctx.fillRect(x, y, 30, 30);
                    } else if ((i + j) % 3 === 1) {
                        ctx.beginPath();
                        ctx.arc(x + 15, y + 15, 15, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                }
            }
        }

        function generatePattern2() {
            const canvas = document.getElementById('pattern2');
            const ctx = canvas.getContext('2d');
            
            // White background
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 400, 400);
            
            // Black border
            ctx.fillStyle = 'black';
            ctx.lineWidth = 4;
            ctx.strokeRect(10, 10, 380, 380);
            
            // Create corner markers
            ctx.fillStyle = 'black';
            ctx.fillRect(20, 20, 60, 60);
            ctx.fillRect(320, 20, 60, 60);
            ctx.fillRect(20, 320, 60, 60);
            
            // White squares in corners
            ctx.fillStyle = 'white';
            ctx.fillRect(30, 30, 40, 40);
            ctx.fillRect(330, 30, 40, 40);
            ctx.fillRect(30, 330, 40, 40);
            
            // Central pattern
            ctx.fillStyle = 'black';
            ctx.fillRect(150, 150, 100, 100);
            ctx.fillStyle = 'white';
            ctx.fillRect(170, 170, 60, 60);
            ctx.fillStyle = 'black';
            ctx.fillRect(185, 185, 30, 30);
        }

        function generatePattern3() {
            const canvas = document.getElementById('pattern3');
            const ctx = canvas.getContext('2d');
            
            // White background
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 400, 400);
            
            // Black border
            ctx.fillStyle = 'black';
            ctx.fillRect(0, 0, 400, 10);
            ctx.fillRect(0, 0, 10, 400);
            ctx.fillRect(390, 0, 10, 400);
            ctx.fillRect(0, 390, 400, 10);
            
            // Text pattern
            ctx.fillStyle = 'black';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('AR', 200, 100);
            ctx.fillText('TEST', 200, 160);
            
            // Add some geometric shapes
            ctx.fillRect(50, 200, 300, 5);
            ctx.fillRect(50, 250, 300, 5);
            
            // Corner triangles
            ctx.beginPath();
            ctx.moveTo(50, 300);
            ctx.lineTo(100, 300);
            ctx.lineTo(75, 350);
            ctx.closePath();
            ctx.fill();
            
            ctx.beginPath();
            ctx.moveTo(300, 300);
            ctx.lineTo(350, 300);
            ctx.lineTo(325, 350);
            ctx.closePath();
            ctx.fill();
        }

        function downloadCanvas(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        // Generate all patterns on load
        generatePattern1();
        generatePattern2();
        generatePattern3();
    </script>
</body>
</html>
